"""
Q1 源数据处理模块
负责轴承故障数据的筛选、特征提取、异常值处理和数据保存
从原始Q1_源数据处理、数据分析及可视化.py文件拆分而来
"""

import os
import numpy as np
import pandas as pd
import scipy.io as sio
from scipy.fft import fft, fftfreq
from scipy.signal import hilbert, resample_poly
try:
    import pywt
    PYWT_AVAILABLE = True
except ImportError:
    PYWT_AVAILABLE = False
    print("警告: pywt未安装，将使用简化的时频域特征提取")
import pickle
import warnings
import scipy.stats

warnings.filterwarnings('ignore')


class BearingDataProcessor:
    """轴承数据处理器 - 专注于数据处理和特征提取"""
    
    def __init__(self, source_data_path):
        self.source_data_path = source_data_path
        self.bearing_params = {
            'SKF6205': {'n': 9, 'd': 0.3126, 'D': 1.537},
            'SKF6203': {'n': 9, 'd': 0.2656, 'D': 1.122}
        }
        self.selected_files = []
        self.features_data = []
        self.window_size = 4096
        self.overlap_ratio = 0.5
        self.target_fs = 32000  # 目标采样率，接近目标域的32kHz

    def get_fault_frequencies(self, rpm, bearing_type='SKF6205'):
        """计算轴承故障特征频率"""
        params = self.bearing_params[bearing_type]
        fr = rpm / 60
        bpfo = fr * (params['n'] / 2) * (1 - params['d'] / params['D'])
        bpfi = fr * (params['n'] / 2) * (1 + params['d'] / params['D'])
        bsf = fr * (params['D'] / params['d']) * (1 - (params['d'] / params['D']) ** 2)
        return {'BPFO': bpfo, 'BPFI': bpfi, 'BSF': bsf, 'FR': fr}

    def resample_signal(self, signal_data, original_fs, target_fs):
        """重采样信号到目标采样率"""
        if original_fs == target_fs:
            return signal_data

        # 计算重采样比例
        if original_fs > target_fs:
            # 降采样
            up = target_fs
            down = original_fs
            # 简化分数
            gcd = np.gcd(up, down)
            up = up // gcd
            down = down // gcd
        else:
            # 升采样
            up = target_fs
            down = original_fs
            gcd = np.gcd(up, down)
            up = up // gcd
            down = down // gcd

        # 使用scipy的resample_poly进行重采样
        resampled = resample_poly(signal_data, up, down)
        return resampled

    def select_source_data(self):
        """筛选源域数据，包含12kHz和48kHz数据"""
        # 数据配置：参考原始文件的完整结构
        data_config = {
            'Normal': {
                '48kHz': {
                    'base_path': '48kHz_Normal_data',
                    'files': ['N_0.mat', 'N_1_(1772rpm).mat', 'N_2_(1750rpm).mat', 'N_3.mat']
                }
            },
            'OR': {
                '48kHz': {
                    'base_path': '48kHz_DE_data/OR',
                    'subdirs': {
                        'Centered': ['0007', '0014', '0021'],
                        'Opposite': ['0007', '0021'],
                        'Orthogonal': ['0007', '0021']
                    },
                    'file_patterns': {
                        'Centered': 'OR{size}@6_{idx}.mat',
                        'Opposite': 'OR{size}@12_{idx}.mat',
                        'Orthogonal': 'OR{size}@3_{idx}.mat'
                    }
                },
                '12kHz': {
                    'base_path': '12kHz_DE_data/OR',
                    'subdirs': {
                        'Centered': ['0007', '0014', '0021'],
                        'Opposite': ['0007', '0021'],
                        'Orthogonal': ['0007', '0021']
                    },
                    'file_patterns': {
                        'Centered': 'OR{size}@6_{idx}.mat',
                        'Opposite': 'OR{size}@12_{idx}.mat',
                        'Orthogonal': 'OR{size}@3_{idx}.mat'
                    }
                }
            },
            'IR': {
                '48kHz': {
                    'base_path': '48kHz_DE_data/IR',
                    'subdirs': ['0007', '0014', '0021'],
                    'file_pattern': 'IR{size}_{idx}.mat'
                },
                '12kHz': {
                    'base_path': '12kHz_DE_data/IR',
                    'subdirs': ['0007', '0014', '0021', '0028'],
                    'file_pattern': 'IR{size}_{idx}.mat',
                    'special_files': {
                        '0028': ['IR028_0_(1797rpm).mat', 'IR028_1_(1772rpm).mat',
                                'IR028_2_(1750rpm).mat', 'IR028_3_(1730rpm).mat']
                    }
                }
            },
            'B': {
                '48kHz': {
                    'base_path': '48kHz_DE_data/B',
                    'subdirs': ['0007', '0014', '0021'],
                    'file_pattern': 'B{size}_{idx}.mat'
                },
                '12kHz': {
                    'base_path': '12kHz_DE_data/B',
                    'subdirs': ['0007', '0014', '0021', '0028'],
                    'file_pattern': 'B{size}_{idx}.mat',
                    'special_files': {
                        '0028': ['B028_0_(1797rpm).mat', 'B028_1_(1772rpm).mat',
                                'B028_2_(1750rpm).mat', 'B028_3_(1730rpm).mat']
                    }
                }
            }
        }

        selected_files = self._build_file_list_from_config(data_config)
        self.selected_files = selected_files

        # 打印筛选结果
        print("=" * 60)
        print("源域数据筛选结果（混合采样率）")
        print("=" * 60)
        for fault_type, files in selected_files.items():
            print(f"\n{fault_type}: {len(files)} 个文件")
            fs_48k = sum(1 for f in files if f['fs'] == 48000)
            fs_12k = sum(1 for f in files if f['fs'] == 12000)
            print(f"  - 48kHz: {fs_48k} 个文件")
            print(f"  - 12kHz: {fs_12k} 个文件")

        total_files = sum(len(files) for files in selected_files.values())
        print(f"\n总计: {total_files} 个文件")

        return selected_files

    def _build_file_list_from_config(self, data_config):
        """根据配置构建文件列表"""
        selected_files = {fault_type: [] for fault_type in data_config.keys()}

        for fault_type, fs_configs in data_config.items():
            for fs_str, config in fs_configs.items():
                fs = int(fs_str.replace('kHz', '000'))

                if fault_type == 'Normal':
                    # 处理正常状态文件
                    for filename in config['files']:
                        file_path = f"{config['base_path']}/{filename}"
                        selected_files[fault_type].append({'path': file_path, 'fs': fs})

                elif 'subdirs' in config and isinstance(config['subdirs'], dict):
                    # 处理OR类型的复杂结构
                    for position, sizes in config['subdirs'].items():
                        pattern = config['file_patterns'][position]
                        for size in sizes:
                            for idx in range(4):  # 0-3
                                # 正确处理前导零：0021 -> 021, 0014 -> 014, 0007 -> 007
                                size_formatted = size[1:] if size.startswith('0') and len(size) == 4 else size
                                filename = pattern.format(size=size_formatted, idx=idx)
                                file_path = f"{config['base_path']}/{position}/{size}/{filename}"
                                selected_files[fault_type].append({'path': file_path, 'fs': fs})

                elif 'subdirs' in config and isinstance(config['subdirs'], list):
                    # 处理IR和B类型的简单结构
                    for size in config['subdirs']:
                        if size in config.get('special_files', {}):
                            # 处理特殊文件（如0028目录的rpm文件）
                            for filename in config['special_files'][size]:
                                file_path = f"{config['base_path']}/{size}/{filename}"
                                selected_files[fault_type].append({'path': file_path, 'fs': fs})
                        else:
                            # 处理标准文件
                            for idx in range(4):  # 0-3
                                # 正确处理前导零：0021 -> 021, 0014 -> 014, 0007 -> 007
                                size_formatted = size[1:] if size.startswith('0') and len(size) == 4 else size
                                filename = config['file_pattern'].format(size=size_formatted, idx=idx)
                                file_path = f"{config['base_path']}/{size}/{filename}"
                                selected_files[fault_type].append({'path': file_path, 'fs': fs})

        return selected_files

    def extract_samples_from_signal(self, signal_data, window_size=None, overlap_ratio=None):
        """将长信号分割成多个样本窗口"""
        if window_size is None:
            window_size = self.window_size
        if overlap_ratio is None:
            overlap_ratio = self.overlap_ratio

        step_size = int(window_size * (1 - overlap_ratio))
        samples = []

        for start in range(0, len(signal_data) - window_size + 1, step_size):
            window = signal_data[start:start + window_size]
            samples.append(window)

        return samples

    def check_signal_quality(self, signal_data):
        """检查信号质量"""
        if np.any(np.isnan(signal_data)) or np.any(np.isinf(signal_data)):
            return False

        signal_std = np.std(signal_data)
        if signal_std < 1e-8:
            return False

        signal_max = np.max(np.abs(signal_data))
        if signal_max > 100 * signal_std:
            return False

        return True

    def load_mat_file(self, file_path):
        """加载MAT文件"""
        full_path = os.path.join(self.source_data_path, file_path)
        try:
            mat_data = sio.loadmat(full_path)

            # 提取DE数据
            de_data = None
            for key in mat_data.keys():
                if 'DE' in key and not key.startswith('__'):
                    de_data = mat_data[key].flatten()
                    break

            # 提取RPM数据
            rpm = 1797  # 默认RPM
            for key in mat_data.keys():
                if 'RPM' in key.upper() and not key.startswith('__'):
                    rpm = float(mat_data[key].flatten()[0])
                    break

            return {
                'DE': de_data,
                'RPM': rpm
            }
        except Exception as e:
            print(f"加载文件失败 {file_path}: {e}")
            return None

    def extract_time_domain_features(self, signal_data):
        """提取时域特征"""
        features = {}

        # 基本统计特征
        features['mean'] = np.mean(signal_data)
        features['std'] = np.std(signal_data)
        features['var'] = np.var(signal_data)
        features['rms'] = np.sqrt(np.mean(signal_data ** 2))
        features['peak'] = np.max(np.abs(signal_data))
        features['peak_to_peak'] = np.ptp(signal_data)

        # 高阶统计特征
        features['skewness'] = scipy.stats.skew(signal_data)
        features['kurtosis'] = scipy.stats.kurtosis(signal_data)

        # 形态特征
        mean_abs = np.mean(np.abs(signal_data))
        if mean_abs > 0:
            features['crest_factor'] = features['peak'] / features['rms'] if features['rms'] > 0 else 0
            features['impulse_factor'] = features['peak'] / mean_abs
            features['shape_factor'] = features['rms'] / mean_abs if mean_abs > 0 else 0
            sqrt_mean_sqrt = np.mean(np.sqrt(np.abs(signal_data)))
            features['clearance_factor'] = features['peak'] / (sqrt_mean_sqrt ** 2) if sqrt_mean_sqrt > 0 else 0
        else:
            features['crest_factor'] = 0
            features['impulse_factor'] = 0
            features['shape_factor'] = 0
            features['clearance_factor'] = 0

        return features

    def extract_frequency_domain_features(self, signal_data, fs, rpm):
        """提取频域特征（修复版本）"""
        features = {}

        # FFT变换
        N = len(signal_data)
        fft_vals = fft(signal_data)
        freqs = fftfreq(N, 1 / fs)[:N // 2]
        magnitude = np.abs(fft_vals[:N // 2])

        # 防止除零错误
        total_magnitude = np.sum(magnitude)
        if total_magnitude == 0:
            return {
                'spectral_centroid': 0, 'spectral_variance': 0, 'spectral_skewness': 0, 'spectral_kurtosis': 0,
                'BPFO_amplitude': 0, 'BPFI_amplitude': 0, 'BSF_amplitude': 0, 'FR_amplitude': 0,
                'low_freq_energy_ratio': 0, 'mid_freq_energy_ratio': 0, 'high_freq_energy_ratio': 0
            }

        # 频谱特征
        features['spectral_centroid'] = np.sum(freqs * magnitude) / total_magnitude
        spectral_variance = np.sum(((freqs - features['spectral_centroid']) ** 2) * magnitude) / total_magnitude
        features['spectral_variance'] = spectral_variance

        if spectral_variance > 0:
            features['spectral_skewness'] = np.sum(((freqs - features['spectral_centroid']) ** 3) * magnitude) / (
                    total_magnitude * spectral_variance ** 1.5)
            features['spectral_kurtosis'] = np.sum(((freqs - features['spectral_centroid']) ** 4) * magnitude) / (
                    total_magnitude * spectral_variance ** 2)
        else:
            features['spectral_skewness'] = 0
            features['spectral_kurtosis'] = 0

        # 故障特征频率分析
        try:
            fault_freqs = self.get_fault_frequencies(rpm)
            freq_resolution = fs / N

            for fault_type, fault_freq in fault_freqs.items():
                freq_idx = int(fault_freq / freq_resolution)
                if 0 <= freq_idx < len(magnitude):
                    features[f'{fault_type}_amplitude'] = magnitude[freq_idx]
                else:
                    features[f'{fault_type}_amplitude'] = 0
        except:
            features['BPFO_amplitude'] = 0
            features['BPFI_amplitude'] = 0
            features['BSF_amplitude'] = 0
            features['FR_amplitude'] = 0

        # 频带能量比
        low_freq_idx = int(0.1 * fs / 2 / freq_resolution)
        mid_freq_idx = int(0.5 * fs / 2 / freq_resolution)

        low_energy = np.sum(magnitude[:low_freq_idx]) if low_freq_idx > 0 else 0
        mid_energy = np.sum(magnitude[low_freq_idx:mid_freq_idx]) if mid_freq_idx > low_freq_idx else 0
        high_energy = np.sum(magnitude[mid_freq_idx:]) if mid_freq_idx < len(magnitude) else 0

        total_energy = low_energy + mid_energy + high_energy
        if total_energy > 0:
            features['low_freq_energy_ratio'] = low_energy / total_energy
            features['mid_freq_energy_ratio'] = mid_energy / total_energy
            features['high_freq_energy_ratio'] = high_energy / total_energy
        else:
            features['low_freq_energy_ratio'] = 0
            features['mid_freq_energy_ratio'] = 0
            features['high_freq_energy_ratio'] = 0

        return features

    def extract_time_frequency_features(self, signal_data, fs):
        """提取时频域特征"""
        features = {}

        try:
            if PYWT_AVAILABLE:
                # 小波包分解
                wp = pywt.WaveletPacket(signal_data, 'db4', mode='symmetric', maxlevel=4)
                levels = 4

                # 提取各频带能量
                energy_features = []
                for i in range(2 ** levels):
                    try:
                        node_name = [node.path for node in wp.get_level(levels, 'freq')][i]
                        coeffs = wp[node_name].data
                        energy = np.sum(coeffs ** 2)
                        energy_features.append(energy)
                    except:
                        energy_features.append(0)

                # 归一化能量特征
                total_energy = sum(energy_features)
                if total_energy > 0:
                    for i, energy in enumerate(energy_features):
                        features[f'wavelet_energy_band_{i}'] = energy / total_energy

                    # 小波熵
                    energy_ratios = np.array(energy_features) / total_energy
                    energy_ratios = energy_ratios[energy_ratios > 0]
                    if len(energy_ratios) > 0:
                        features['wavelet_entropy'] = -np.sum(energy_ratios * np.log2(energy_ratios + 1e-12))
                    else:
                        features['wavelet_entropy'] = 0
                else:
                    for i in range(2 ** levels):
                        features[f'wavelet_energy_band_{i}'] = 0
                    features['wavelet_entropy'] = 0
            else:
                # 简化版本：使用滤波器组近似小波包分解
                for i in range(16):
                    # 使用简单的频带能量作为替代
                    band_start = i * fs // 32
                    band_end = (i + 1) * fs // 32
                    if band_end <= fs // 2:
                        fft_vals = np.fft.fft(signal_data)
                        freqs = np.fft.fftfreq(len(signal_data), 1/fs)
                        band_mask = (np.abs(freqs) >= band_start) & (np.abs(freqs) < band_end)
                        band_energy = np.sum(np.abs(fft_vals[band_mask]) ** 2)
                        features[f'wavelet_energy_band_{i}'] = band_energy
                    else:
                        features[f'wavelet_energy_band_{i}'] = 0

                # 简化的小波熵
                total_energy = sum([features[f'wavelet_energy_band_{i}'] for i in range(16)])
                if total_energy > 0:
                    energy_ratios = np.array([features[f'wavelet_energy_band_{i}'] for i in range(16)]) / total_energy
                    energy_ratios = energy_ratios[energy_ratios > 0]
                    if len(energy_ratios) > 0:
                        features['wavelet_entropy'] = -np.sum(energy_ratios * np.log2(energy_ratios + 1e-12))
                    else:
                        features['wavelet_entropy'] = 0
                else:
                    features['wavelet_entropy'] = 0

            # 包络分析
            analytic_signal = hilbert(signal_data)
            envelope = np.abs(analytic_signal)

            features['envelope_mean'] = np.mean(envelope)
            features['envelope_std'] = np.std(envelope)
            features['envelope_skewness'] = scipy.stats.skew(envelope)
            features['envelope_kurtosis'] = scipy.stats.kurtosis(envelope)

        except Exception as e:
            print(f"时频特征提取失败: {e}")
            # 设置默认值
            for i in range(16):
                features[f'wavelet_energy_band_{i}'] = 0
            features['wavelet_entropy'] = 0
            features['envelope_mean'] = 0
            features['envelope_std'] = 0
            features['envelope_skewness'] = 0
            features['envelope_kurtosis'] = 0

        return features

    def extract_features(self, signal_data, sensor_type, fs, rpm, file_info):
        """提取完整特征集"""
        features = {}

        # 添加文件信息
        features['file_path'] = file_info['file_path']
        features['fault_type'] = file_info['fault_type']
        features['sensor_type'] = sensor_type
        features['rpm'] = rpm
        features['original_fs'] = file_info['original_fs']
        features['resampled_fs'] = fs

        # 去除直流分量
        signal_data = signal_data - np.mean(signal_data)

        # 时域特征
        time_features = self.extract_time_domain_features(signal_data)
        for key, value in time_features.items():
            features[f'{sensor_type}_{key}'] = value

        # 频域特征
        freq_features = self.extract_frequency_domain_features(signal_data, fs, rpm)
        for key, value in freq_features.items():
            features[f'{sensor_type}_{key}'] = value

        # 时频域特征
        time_freq_features = self.extract_time_frequency_features(signal_data, fs)
        for key, value in time_freq_features.items():
            features[f'{sensor_type}_{key}'] = value

        return features

    def process_all_selected_files(self):
        """处理所有选定的文件（包含重采样）"""
        print("\n" + "=" * 60)
        print("开始特征提取（混合采样率 + 重采样）")
        print("=" * 60)
        print(f"目标采样率: {self.target_fs} Hz")
        print(f"窗口参数: 窗口大小={self.window_size}, 重叠率={self.overlap_ratio}")

        all_features = []
        total_samples = 0

        for fault_type, file_list in self.selected_files.items():
            print(f"\n处理 {fault_type} 故障类型...")
            fault_samples = 0

            for file_info in file_list:
                file_path = file_info['path']
                original_fs = file_info['fs']
                print(f"  处理文件: {file_path} (原始采样率: {original_fs} Hz)")

                # 加载文件
                mat_data = self.load_mat_file(file_path)
                if mat_data is None:
                    continue

                rpm = mat_data['RPM']

                # 处理DE数据（主要传感器）
                if mat_data['DE'] is not None:
                    signal_length = len(mat_data['DE'])
                    print(f"    原始信号长度: {signal_length} 点 ({signal_length / original_fs:.2f} 秒)")

                    # 重采样到目标采样率
                    resampled_signal = self.resample_signal(mat_data['DE'], original_fs, self.target_fs)
                    print(f"    重采样后长度: {len(resampled_signal)} 点 ({len(resampled_signal) / self.target_fs:.2f} 秒)")

                    # 分割信号为多个样本
                    samples = self.extract_samples_from_signal(resampled_signal)
                    valid_samples = 0

                    for i, sample in enumerate(samples):
                        # 检查样本质量
                        if not self.check_signal_quality(sample):
                            continue

                        file_info_dict = {
                            'file_path': f"{file_path}_sample_{i}",
                            'fault_type': fault_type,
                            'original_fs': original_fs
                        }

                        # 提取特征
                        features = self.extract_features(sample, 'DE', self.target_fs, rpm, file_info_dict)
                        all_features.append(features)
                        valid_samples += 1

                    print(f"    从DE信号中提取了 {valid_samples} 个有效样本")
                    fault_samples += valid_samples

            print(f"  {fault_type} 总样本数: {fault_samples}")
            total_samples += fault_samples

        self.features_data = all_features
        print(f"\n特征提取完成:")
        print(f"  总样本数: {len(all_features)}")

        # 打印各类别样本统计
        if all_features:
            df_temp = pd.DataFrame(all_features)
            print(f"\n各类别样本数:")
            for fault_type in ['Normal', 'OR', 'IR', 'B']:
                count = len(df_temp[df_temp['fault_type'] == fault_type])
                print(f"    {fault_type}: {count}")

            # 打印原始采样率分布
            print(f"\n原始采样率分布:")
            fs_counts = df_temp['original_fs'].value_counts()
            for fs, count in fs_counts.items():
                print(f"    {fs} Hz: {count}")

        return all_features

    def detect_outliers_by_category(self, df, method='iqr', iqr_factor=1.5, z_threshold=3):
        """分类别检测异常值"""
        exclude_cols = ['file_path', 'fault_type', 'sensor_type', 'rpm', 'original_fs', 'resampled_fs']
        feature_cols = [col for col in df.columns if col not in exclude_cols]

        outlier_indices = set()

        for fault_type in df['fault_type'].unique():
            category_data = df[df['fault_type'] == fault_type]
            category_outliers = set()

            print(f"\n检测 {fault_type} 类别异常值:")

            for col in feature_cols:
                values = category_data[col].values
                if len(values) == 0:
                    continue

                col_outliers = set()

                if method in ['iqr', 'both']:
                    Q1 = np.percentile(values, 25)
                    Q3 = np.percentile(values, 75)
                    IQR = Q3 - Q1
                    lower_bound = Q1 - iqr_factor * IQR
                    upper_bound = Q3 + iqr_factor * IQR
                    iqr_outliers = category_data[(category_data[col] < lower_bound) |
                                               (category_data[col] > upper_bound)].index
                    col_outliers.update(iqr_outliers)

                if method in ['zscore', 'both']:
                    z_scores = np.abs(scipy.stats.zscore(values))
                    zscore_outliers = category_data.iloc[z_scores > z_threshold].index
                    col_outliers.update(zscore_outliers)

                category_outliers.update(col_outliers)

            print(f"  检测到 {len(category_outliers)} 个异常值 ({len(category_outliers) / len(category_data) * 100:.2f}%)")
            outlier_indices.update(category_outliers)

        return outlier_indices

    def remove_outliers(self, method='iqr', iqr_factor=1.5, z_threshold=3, max_removal_ratio=0.1):
        """分类别移除异常值"""
        if not self.features_data:
            print("没有特征数据可处理")
            return

        print("=" * 60)
        print("分类别异常值检测与移除")
        print("=" * 60)
        print(f"检测方法: {method}")
        if method == 'iqr' or method == 'both':
            print(f"IQR倍数: {iqr_factor}")
        if method == 'zscore' or method == 'both':
            print(f"Z-score阈值: {z_threshold}")
        print(f"每类最大移除比例: {max_removal_ratio * 100:.1f}%")

        # 转换为DataFrame
        df = pd.DataFrame(self.features_data)
        original_counts = df['fault_type'].value_counts()

        print(f"\n原始数据分布:")
        for fault_type in ['Normal', 'OR', 'IR', 'B']:
            count = original_counts.get(fault_type, 0)
            print(f"  {fault_type}: {count}")

        # 检测异常值
        outlier_indices = self.detect_outliers_by_category(df, method, iqr_factor, z_threshold)

        # 按类别限制移除数量
        indices_to_remove = set()
        for fault_type in df['fault_type'].unique():
            category_data = df[df['fault_type'] == fault_type]
            category_outliers = [idx for idx in outlier_indices if idx in category_data.index]

            max_remove = int(len(category_data) * max_removal_ratio)
            if len(category_outliers) > max_remove:
                category_outliers = category_outliers[:max_remove]

            indices_to_remove.update(category_outliers)

        # 移除异常值
        df_cleaned = df.drop(indices_to_remove).reset_index(drop=True)

        # 更新特征数据
        self.features_data = df_cleaned.to_dict('records')

        # 打印移除结果
        print(f"\n异常值移除结果:")
        print(f"{'类别':<8} {'原始':<8} {'移除':<8} {'剩余':<8} {'移除率':<8}")
        print("-" * 50)

        cleaned_counts = df_cleaned['fault_type'].value_counts()
        total_original = len(df)
        total_removed = len(indices_to_remove)
        total_remaining = len(df_cleaned)

        for fault_type in ['Normal', 'OR', 'IR', 'B']:
            original = original_counts.get(fault_type, 0)
            remaining = cleaned_counts.get(fault_type, 0)
            removed = original - remaining
            removal_ratio = removed / original * 100 if original > 0 else 0

            print(f"{fault_type:<8} {original:<8} {removed:<8} {remaining:<8} {removal_ratio:<7.1f}%")

        overall_removal_ratio = total_removed / total_original
        print("-" * 50)
        print(f"{'总计':<8} {total_original:<8} {total_removed:<8} {total_remaining:<8} {overall_removal_ratio * 100:<7.1f}%")

        print(f"\n异常值处理完成:")
        print(f"  原始样本数: {total_original}")
        print(f"  移除样本数: {total_removed}")
        print(f"  保留样本数: {total_remaining}")
        print(f"  整体移除率: {overall_removal_ratio * 100:.2f}%")

    def save_processed_data(self, output_dir='processed_data_mixed_fs'):
        """保存处理后的数据"""
        os.makedirs(output_dir, exist_ok=True)

        # 保存筛选的文件列表
        with open(os.path.join(output_dir, 'selected_files.pickle'), 'wb') as f:
            pickle.dump(self.selected_files, f)

        # 保存处理参数
        processing_params = {
            'window_size': self.window_size,
            'overlap_ratio': self.overlap_ratio,
            'target_fs': self.target_fs,
            'original_fs_list': [12000, 48000]
        }
        with open(os.path.join(output_dir, 'processing_params.pickle'), 'wb') as f:
            pickle.dump(processing_params, f)

        # 转换为DataFrame并保存
        if self.features_data:
            df = pd.DataFrame(self.features_data)

            # 保存为CSV
            df.to_csv(os.path.join(output_dir, 'extracted_features.csv'), index=False)

            # 保存为pickle
            df.to_pickle(os.path.join(output_dir, 'extracted_features.pkl'))

            # 保存特征统计信息
            feature_stats = df.describe()
            feature_stats.to_csv(os.path.join(output_dir, 'feature_statistics.csv'))

            # 按故障类型分别保存
            for fault_type in ['Normal', 'OR', 'IR', 'B']:
                fault_data = df[df['fault_type'] == fault_type]
                if not fault_data.empty:
                    fault_data.to_csv(os.path.join(output_dir, f'features_{fault_type}.csv'), index=False)

            print(f"\n数据保存完成，输出目录: {output_dir}")
            print(f"  - 特征数据: extracted_features.csv/pkl")
            print(f"  - 文件列表: selected_files.pickle")
            print(f"  - 处理参数: processing_params.pickle")
            print(f"  - 统计信息: feature_statistics.csv")
            print(f"  - 分类数据: features_Normal.csv, features_OR.csv, features_IR.csv, features_B.csv")

            # 打印数据集摘要
            print(f"\n数据集摘要:")
            print(f"  总样本数: {len(df)}")
            print(f"  特征维度: {len(df.columns)}")
            print("  各类样本数:")
            for fault_type in ['Normal', 'OR', 'IR', 'B']:
                count = len(df[df['fault_type'] == fault_type])
                percentage = count / len(df) * 100
                print(f"    {fault_type}: {count} ({percentage:.1f}%)")

            # 混合采样率统计
            print("\n  原始采样率分布:")
            fs_counts = df['original_fs'].value_counts()
            for fs, count in fs_counts.items():
                percentage = count / len(df) * 100
                print(f"    {fs} Hz: {count} ({percentage:.1f}%)")

        return output_dir


def main():
    """主函数 - 源数据处理流程"""
    print("=" * 80)
    print("Q1 源数据处理")
    print("=" * 80)
    print("功能：数据筛选 → 特征提取 → 异常值处理 → 数据保存")

    source_data_path = r"源域数据集"
    processor = BearingDataProcessor(source_data_path)

    # 步骤1: 筛选源数据
    print("\n步骤1: 筛选源数据")
    processor.select_source_data()

    # 步骤2: 处理所有文件并提取特征
    print("\n步骤2: 特征提取")
    processor.process_all_selected_files()

    # 步骤3: 异常值处理
    print("\n步骤3: 异常值处理")
    processor.remove_outliers(method='both', iqr_factor=2.0, z_threshold=3.5, max_removal_ratio=0.05)

    # 步骤4: 保存处理后的数据
    print("\n步骤4: 保存数据")
    output_dir = processor.save_processed_data()

    print(f"\n源数据处理完成！")
    print(f"输出目录: {output_dir}")
    print(f"生成的文件可用于后续的分析和建模")


if __name__ == "__main__":
    main()
